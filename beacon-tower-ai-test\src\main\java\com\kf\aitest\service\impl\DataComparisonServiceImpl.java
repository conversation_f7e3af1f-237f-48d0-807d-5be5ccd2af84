package com.kf.aitest.service.impl;

import com.kf.aitest.dto.DataComparisonRequestDTO;
import com.kf.aitest.dto.DataComparisonResultDTO;
import com.kf.aitest.dto.StageDataDTO;
import com.kf.aitest.service.AiEvaluationService;
import com.kf.aitest.service.ComparisonProgressManager;
import com.kf.aitest.entity.TDataComparison;
import com.kf.aitest.service.DataComparisonService;
import com.kf.aitest.service.DataComparisonStorageService;
import com.kf.aitest.service.DataFetchService;
import com.kf.aitest.service.ResultPrintService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;

/**
 * 数据对比服务实现
 */
@Slf4j
@Service
public class DataComparisonServiceImpl implements DataComparisonService {
    
    @Autowired
    private DataFetchService dataFetchService;

    @Autowired
    private AiEvaluationService aiEvaluationService;

    @Autowired
    private ComparisonProgressManager progressManager;

    @Autowired
    private ResultPrintService resultPrintService;

    @Autowired
    private DataComparisonStorageService storageService;

    @Value("${data.comparison.uat.url:https://copilot-uat.pharmaronclinical.com}")
    private String defaultUatUrl;
    
    @Value("${data.comparison.test.url:https://copilot-test.pharmaronclinical.com}")
    private String defaultTestUrl;
    
    // 数据处理的四个阶段，按顺序执行
    private static final List<String> DEFAULT_STAGES = Arrays.asList("recognize", "extraction", "structured", "transformer");
    
    @Override
    public SseEmitter createSseConnection(String taskId) {
        return progressManager.createEmitter(taskId);
    }
    
    @Override
    @Async
    public void startComparison(String taskId, DataComparisonRequestDTO request) {
        log.info("开始数据对比任务: taskId={}, ids={}", taskId, request.getIds());

        // 打印任务开始信息
        resultPrintService.printTaskStart(taskId, request.getIds().size());

        try {
            // 初始化进度
            progressManager.initProgress(taskId, request.getIds().size());
            
            // 获取环境URL
            String uatUrl = request.getUatBaseUrl() != null ? request.getUatBaseUrl() : getDefaultUatUrl();
            String testUrl = request.getTestBaseUrl() != null ? request.getTestBaseUrl() : getDefaultTestUrl();
            
            // 创建并发控制信号量
            Semaphore semaphore = new Semaphore(request.getConcurrentLimit());
            
            // 处理每个ID
            List<CompletableFuture<DataComparisonResultDTO>> futures = new ArrayList<>();
            
            for (int i = 0; i < request.getIds().size(); i++) {
                String id = request.getIds().get(i);
                int idIndex = i;
                
                CompletableFuture<DataComparisonResultDTO> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        semaphore.acquire();
                        return processId(taskId, id, idIndex, uatUrl, testUrl, request);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.error("任务被中断: taskId={}, id={}", taskId, id);
                        progressManager.sendError(taskId, "任务被中断: " + id);
                        throw new RuntimeException("任务被中断", e);
                    } catch (Exception e) {
                        log.error("处理ID异常: taskId={}, id={}, error={}", taskId, id, e.getMessage(), e);
                        progressManager.sendError(taskId, "处理ID失败: " + id + ", " + e.getMessage());
                        throw e;
                    } finally {
                        semaphore.release();
                    }
                });
                
                futures.add(future);
            }
            
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            // 完成任务
            progressManager.completeTask(taskId);
            log.info("数据对比任务完成: taskId={}", taskId);

            // 打印任务完成信息
            resultPrintService.printTaskComplete(taskId);
            
        } catch (Exception e) {
            log.error("数据对比任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            String errorMessage = "任务执行失败: " + e.getMessage();
            progressManager.sendError(taskId, errorMessage);
        }
    }
    
    /**
     * 处理单个ID的数据对比
     */
    private DataComparisonResultDTO processId(String taskId, String id, int idIndex,
                                            String uatUrl, String testUrl, DataComparisonRequestDTO request) {
        log.info("开始处理ID: taskId={}, id={}, index={}", taskId, id, idIndex);

        DataComparisonResultDTO result = new DataComparisonResultDTO();
        result.setId(id);
        result.setTaskId(taskId);
        result.setStartTime(LocalDateTime.now());
        result.setStageResults(new ArrayList<>());
        result.setErrors(new ArrayList<>());

        // 更新当前处理的ID
        progressManager.updateCurrentId(taskId, id, idIndex);

        int successCount = 0;

        // 确定要处理的阶段列表
        List<String> stagesToProcess = request.getStages() != null && !request.getStages().isEmpty()
                ? request.getStages()
                : DEFAULT_STAGES;

        log.info("处理阶段列表: {}", stagesToProcess);

        try {
            // 按顺序处理指定的阶段
            for (int stageIndex = 0; stageIndex < stagesToProcess.size(); stageIndex++) {
                String stageName = stagesToProcess.get(stageIndex);

                // 验证阶段名称是否有效
                if (!DEFAULT_STAGES.contains(stageName)) {
                    log.warn("无效的阶段名称: {}, 跳过处理", stageName);
                    continue;
                }

                try {
                    // 打印阶段开始信息
                    resultPrintService.printStageSeparator(stageName, id);

                    // 更新当前阶段
                    progressManager.updateCurrentStage(taskId, stageName, stageIndex);

                    // 获取阶段数据
                    StageDataDTO stageData = dataFetchService.fetchStageData(
                            id, stageName, uatUrl, testUrl, request.getTimeoutSeconds());

                    // AI评估（如果启用）
                    if (request.getEnableAiEvaluation() && "SUCCESS".equals(stageData.getStatus())) {
                        // 检查是否为AiEvaluationServiceImpl实例，支持禁用分片
                        if (aiEvaluationService instanceof com.kf.aitest.service.impl.AiEvaluationServiceImpl) {
                            com.kf.aitest.service.impl.AiEvaluationServiceImpl impl =
                                (com.kf.aitest.service.impl.AiEvaluationServiceImpl) aiEvaluationService;
                            stageData = impl.evaluateStageData(taskId, stageData,
                                request.getDisableChunking() != null ? request.getDisableChunking() : false);
                        } else {
                            stageData = aiEvaluationService.evaluateStageData(taskId, stageData);
                        }
                    }

                    result.getStageResults().add(stageData);

                    // 打印阶段结果
                    resultPrintService.printStageResult(stageData, id);

                    if ("SUCCESS".equals(stageData.getStatus())) {
                        successCount++;
                    } else {
                        result.getErrors().add(String.format("阶段%s失败: %s", stageName, stageData.getErrorMessage()));
                    }

                    // 完成阶段
                    progressManager.completeStage(taskId, stageName);

                } catch (Exception e) {
                    log.error("处理阶段失败: id={}, stage={}, error={}", id, stageName, e.getMessage(), e);

                    StageDataDTO errorStage = new StageDataDTO();
                    errorStage.setStageName(stageName);
                    errorStage.setStatus("FAILED");
                    errorStage.setErrorMessage(e.getMessage());
                    errorStage.setFetchTime(LocalDateTime.now());

                    result.getStageResults().add(errorStage);
                    result.getErrors().add(String.format("阶段%s异常: %s", stageName, e.getMessage()));
                }
            }

            // 设置结果状态
            result.setSuccessStageCount(successCount);
            result.setTotalStageCount(stagesToProcess.size());
            if (successCount == stagesToProcess.size()) {
                result.setOverallStatus("SUCCESS");
            } else if (successCount > 0) {
                result.setOverallStatus("PARTIAL_SUCCESS");
            } else {
                result.setOverallStatus("FAILED");
            }

            // 整体AI评估（如果启用且有成功的阶段）
            if (request.getEnableAiEvaluation() && successCount > 0) {
                result = aiEvaluationService.evaluateOverallResult(taskId, result);

                // 打印整体评估结果
                resultPrintService.printOverallResult(result);
            }

        } catch (Exception e) {
            log.error("处理ID失败: id={}, error={}", id, e.getMessage(), e);
            result.setOverallStatus("FAILED");
            result.getErrors().add("处理失败: " + e.getMessage());
        } finally {
            result.setEndTime(LocalDateTime.now());
            if (result.getStartTime() != null && result.getEndTime() != null) {
                result.setTotalDuration(java.time.Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
            }

            // 保存对比结果到数据库
            try {
                if (request.getUserId() != null) {
                    // 保存主表数据
                    TDataComparison comparison = storageService.saveComparisonResult(result, request.getUserId());
                    log.info("对比结果已保存到数据库: taskId={}, id={}, comparisonId={}", taskId, id, comparison.getId());

                    // 保存阶段数据
                    if (comparison.getId() != null && result.getStageResults() != null && !result.getStageResults().isEmpty()) {
                        try {
                            storageService.batchSaveStageResults(comparison.getId(), result.getStageResults());
                            log.info("阶段数据已保存到数据库: comparisonId={}, stageCount={}",
                                    comparison.getId(), result.getStageResults().size());
                        } catch (Exception stageException) {
                            log.error("保存阶段数据失败: comparisonId={}, error={}",
                                    comparison.getId(), stageException.getMessage(), stageException);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("保存对比结果到数据库失败: taskId={}, id={}, error={}", taskId, id, e.getMessage(), e);
            }
        }

        // 完成ID处理
        progressManager.completeId(taskId, id, result);

        log.info("完成ID处理: id={}, status={}, successStages={}/{}",
                id, result.getOverallStatus(), successCount, stagesToProcess.size());

        return result;
    }

    @Override
    public String getDefaultUatUrl() {
        return defaultUatUrl;
    }

    @Override
    public String getDefaultTestUrl() {
        return defaultTestUrl;
    }
}
