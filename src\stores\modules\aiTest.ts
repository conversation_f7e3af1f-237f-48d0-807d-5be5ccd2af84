/**
 * AI测试模块的Pinia Store
 * 管理全局的AI测试状态和数据
 */

import { defineStore } from 'pinia'
import { ref, reactive, computed, readonly } from 'vue'
// import type { SSEEventData } from '@/api/ai-test'

// 临时类型定义，避免导入错误
interface SSEEventData {
  type: string
  data: any
  timestamp?: Date
}

// AI测试任务接口
export interface AITestTask {
  id: string
  name: string
  status: 'pending' | 'running' | 'completed' | 'error'
  progress: number
  startTime: Date | null
  endTime: Date | null
  error: string | null
  results: any[]
}

// AI测试会话接口
export interface AITestSession {
  id: string
  taskId: string
  events: SSEEventData[]
  isActive: boolean
  createdAt: Date
}

export const useAITestStore = defineStore('aiTest', () => {
    // 状态
    const tasks = ref<Map<string, AITestTask>>(new Map())
    const sessions = ref<Map<string, AITestSession>>(new Map())
    const activeTaskId = ref<string | null>(null)
    const activeSessionId = ref<string | null>(null)
  
    // 全局配置
    const globalConfig = reactive({
        enableAiEvaluation: true,
        disableChunking: false,
        maxTokens: 8000,
        timeout: 120000,
        autoSaveResults: true,
        maxHistoryCount: 100
    })

    // 统计信息
    const stats = reactive({
        totalTasks: 0,
        completedTasks: 0,
        errorTasks: 0,
        totalEvents: 0,
        lastUpdateTime: null as Date | null
    })

    // 计算属性
    const activeTasks = computed(() => {
        return Array.from(tasks.value.values()).filter(task => 
            task.status === 'running' || task.status === 'pending'
        )
    })

    const completedTasks = computed(() => {
        return Array.from(tasks.value.values()).filter(task => 
            task.status === 'completed'
        )
    })

    const errorTasks = computed(() => {
        return Array.from(tasks.value.values()).filter(task => 
            task.status === 'error'
        )
    })

    const activeTask = computed(() => {
        return activeTaskId.value ? tasks.value.get(activeTaskId.value) : null
    })

    const activeSession = computed(() => {
        return activeSessionId.value ? sessions.value.get(activeSessionId.value) : null
    })

    const hasActiveTasks = computed(() => activeTasks.value.length > 0)

    // 任务管理方法
    const createTask = (taskId: string, name: string = '未命名任务'): AITestTask => {
        const task: AITestTask = {
            id: taskId,
            name,
            status: 'pending',
            progress: 0,
            startTime: null,
            endTime: null,
            error: null,
            results: []
        }
    
        tasks.value.set(taskId, task)
        stats.totalTasks++
        updateStats()
    
        return task
    }

    const updateTask = (taskId: string, updates: Partial<AITestTask>) => {
        const task = tasks.value.get(taskId)
        if (task) {
            Object.assign(task, updates)
            updateStats()
        }
    }

    const deleteTask = (taskId: string) => {
        const task = tasks.value.get(taskId)
        if (task) {
            tasks.value.delete(taskId)
      
            // 如果删除的是当前活动任务，清除活动状态
            if (activeTaskId.value === taskId) {
                activeTaskId.value = null
            }
      
            // 删除相关会话
            const relatedSessions = Array.from(sessions.value.values())
                .filter(session => session.taskId === taskId)
      
            relatedSessions.forEach(session => {
                sessions.value.delete(session.id)
            })
      
            updateStats()
        }
    }

    const setActiveTask = (taskId: string | null) => {
        activeTaskId.value = taskId
    }

    // 会话管理方法
    const createSession = (taskId: string): AITestSession => {
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
        const session: AITestSession = {
            id: sessionId,
            taskId,
            events: [],
            isActive: true,
            createdAt: new Date()
        }
    
        // 停用其他会话
        sessions.value.forEach(s => {
            if (s.taskId === taskId) {
                s.isActive = false
            }
        })
    
        sessions.value.set(sessionId, session)
        activeSessionId.value = sessionId
    
        return session
    }

    const addEventToSession = (sessionId: string, event: SSEEventData) => {
        const session = sessions.value.get(sessionId)
        if (session) {
            session.events.unshift(event)
            stats.totalEvents++
      
            // 限制事件数量，避免内存泄漏
            if (session.events.length > 1000) {
                session.events = session.events.slice(0, 500)
            }
      
            updateStats()
        }
    }

    const closeSession = (sessionId: string) => {
        const session = sessions.value.get(sessionId)
        if (session) {
            session.isActive = false
      
            if (activeSessionId.value === sessionId) {
                activeSessionId.value = null
            }
        }
    }

    const deleteSession = (sessionId: string) => {
        sessions.value.delete(sessionId)
    
        if (activeSessionId.value === sessionId) {
            activeSessionId.value = null
        }
    }

    // 统计信息更新
    const updateStats = () => {
        const allTasks = Array.from(tasks.value.values())
    
        stats.totalTasks = allTasks.length
        stats.completedTasks = allTasks.filter(t => t.status === 'completed').length
        stats.errorTasks = allTasks.filter(t => t.status === 'error').length
        stats.lastUpdateTime = new Date()
    }

    // 数据清理方法
    const clearCompletedTasks = () => {
        const completedTaskIds = Array.from(tasks.value.entries())
            .filter(([_, task]) => task.status === 'completed')
            .map(([id, _]) => id)
    
        completedTaskIds.forEach(taskId => deleteTask(taskId))
    }

    const clearErrorTasks = () => {
        const errorTaskIds = Array.from(tasks.value.entries())
            .filter(([_, task]) => task.status === 'error')
            .map(([id, _]) => id)
    
        errorTaskIds.forEach(taskId => deleteTask(taskId))
    }

    const clearAllTasks = () => {
        tasks.value.clear()
        sessions.value.clear()
        activeTaskId.value = null
        activeSessionId.value = null
    
        stats.totalTasks = 0
        stats.completedTasks = 0
        stats.errorTasks = 0
        stats.totalEvents = 0
        stats.lastUpdateTime = new Date()
    }

    // 配置管理
    const updateGlobalConfig = (config: Partial<typeof globalConfig>) => {
        Object.assign(globalConfig, config)
    }

    // 数据导出
    const exportTaskData = (taskId: string) => {
        const task = tasks.value.get(taskId)
        if (!task) return null
    
        const relatedSessions = Array.from(sessions.value.values())
            .filter(session => session.taskId === taskId)
    
        return {
            task,
            sessions: relatedSessions,
            exportTime: new Date()
        }
    }

    const exportAllData = () => {
        return {
            tasks: Array.from(tasks.value.values()),
            sessions: Array.from(sessions.value.values()),
            config: globalConfig,
            stats,
            exportTime: new Date()
        }
    }

    return {
    // 状态
        tasks: readonly(tasks),
        sessions: readonly(sessions),
        activeTaskId: readonly(activeTaskId),
        activeSessionId: readonly(activeSessionId),
        globalConfig,
        stats: readonly(stats),
    
        // 计算属性
        activeTasks,
        completedTasks,
        errorTasks,
        activeTask,
        activeSession,
        hasActiveTasks,
    
        // 任务管理方法
        createTask,
        updateTask,
        deleteTask,
        setActiveTask,
    
        // 会话管理方法
        createSession,
        addEventToSession,
        closeSession,
        deleteSession,
    
        // 数据清理方法
        clearCompletedTasks,
        clearErrorTasks,
        clearAllTasks,
    
        // 配置管理
        updateGlobalConfig,
    
        // 数据导出
        exportTaskData,
        exportAllData
    }
})

// 导出类型
export { type AITestTask, type AITestSession }
