/**
 * SSE连接管理Composable
 * 提供Server-Sent Events连接的通用逻辑
 */

import { ref, reactive, onUnmounted, readonly } from 'vue'
import { ElMessage } from 'element-plus'
// import {
//     createAITestSSEConnection,
//     type AITestSSEConnection,
//     type SSEEventData,
//     type SSEEventCallbacks,
//     type SSEConnectionStatus
// } from '@/api/ai-test'

// 临时类型定义，避免导入错误
interface SSEEventData {
  type: string
  data: any
  timestamp?: Date
}

interface AITestSSEConnection {
  close: () => void
}

interface SSEEventCallbacks {
  onProgress: (data: SSEEventData) => void
  onAiEvaluation: (data: SSEEventData) => void
  onAiResult: (data: SSEEventData) => void
  onStageComplete: (data: SSEEventData) => void
  onError: (error: string) => void
  onClose: () => void
}

// type SSEConnectionStatus = 'connected' | 'disconnected' | 'error'

// 临时函数定义，避免导入错误
const createAITestSSEConnection = async (taskId: string, callbacks: SSEEventCallbacks): Promise<AITestSSEConnection> => {
    console.log('Mock createAITestSSEConnection called with:', taskId)
    return {
        close: () => {
            console.log('Mock SSE connection closed')
            callbacks.onClose()
        }
    }
}

// SSE连接状态枚举
export enum SSEStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
  RECONNECTING = 'reconnecting'
}

// SSE连接配置接口
export interface SSEConfig {
  taskId: string
  autoReconnect: boolean
  maxReconnectAttempts: number
  reconnectInterval: number
}

// SSE连接状态接口
export interface SSEState {
  status: SSEStatus
  connection: AITestSSEConnection | null
  lastEventTime: Date | null
  reconnectAttempts: number
  error: string | null
  eventCount: number
}

/**
 * SSE连接Composable
 */
export function useSSEConnection() {
    // 响应式状态
    const state = reactive<SSEState>({
        status: SSEStatus.DISCONNECTED,
        connection: null,
        lastEventTime: null,
        reconnectAttempts: 0,
        error: null,
        eventCount: 0
    })

    const config = reactive<SSEConfig>({
        taskId: '',
        autoReconnect: true,
        maxReconnectAttempts: 5,
        reconnectInterval: 3000
    })

    const events = ref<SSEEventData[]>([])
    const connecting = ref(false)

    // 事件回调函数
    const callbacks = reactive<SSEEventCallbacks>({
        onProgress: (data) => {
            console.log('Progress event:', data)
            addEvent(data)
        },
        onAiEvaluation: (data) => {
            console.log('AI Evaluation event:', data)
            addEvent(data)
        },
        onAiResult: (data) => {
            console.log('AI Result event:', data)
            addEvent(data)
        },
        onStageComplete: (data) => {
            console.log('Stage Complete event:', data)
            addEvent(data)
        },
        onError: (error) => {
            console.error('SSE Error:', error)
            state.error = error
            state.status = SSEStatus.ERROR
            ElMessage.error(`SSE连接错误: ${error}`)
        },
        onClose: () => {
            console.log('SSE Connection closed')
            state.status = SSEStatus.DISCONNECTED
            state.connection = null
      
            // 自动重连逻辑
            if (config.autoReconnect && state.reconnectAttempts < config.maxReconnectAttempts) {
                setTimeout(() => {
                    reconnect()
                }, config.reconnectInterval)
            }
        }
    })

    // 添加事件到列表
    const addEvent = (eventData: SSEEventData) => {
        events.value.unshift({
            ...eventData,
            timestamp: new Date()
        })
        state.lastEventTime = new Date()
        state.eventCount++
    
        // 限制事件列表长度，避免内存泄漏
        if (events.value.length > 1000) {
            events.value = events.value.slice(0, 500)
        }
    }

    // 连接SSE
    const connect = async (taskId: string): Promise<boolean> => {
        if (!taskId) {
            ElMessage.error('请提供任务ID')
            return false
        }

        if (state.status === SSEStatus.CONNECTED || state.status === SSEStatus.CONNECTING) {
            ElMessage.warning('连接已存在或正在连接中')
            return false
        }

        connecting.value = true
        state.status = SSEStatus.CONNECTING
        state.error = null
        config.taskId = taskId

        try {
            const connection = await createAITestSSEConnection(taskId, callbacks)
      
            state.connection = connection
            state.status = SSEStatus.CONNECTED
            state.reconnectAttempts = 0
      
            ElMessage.success('SSE连接建立成功')
            return true
        } catch (error) {
            state.status = SSEStatus.ERROR
            state.error = error instanceof Error ? error.message : 'SSE连接失败'
            ElMessage.error(state.error)
            return false
        } finally {
            connecting.value = false
        }
    }

    // 断开连接
    const disconnect = () => {
        if (state.connection) {
            state.connection.close()
            state.connection = null
        }
        state.status = SSEStatus.DISCONNECTED
        state.error = null
        config.autoReconnect = false // 主动断开时禁用自动重连
    }

    // 重连
    const reconnect = async (): Promise<boolean> => {
        if (!config.taskId) {
            return false
        }

        state.reconnectAttempts++
        state.status = SSEStatus.RECONNECTING
    
        console.log(`尝试重连 (${state.reconnectAttempts}/${config.maxReconnectAttempts})`)
    
        const success = await connect(config.taskId)
    
        if (!success && state.reconnectAttempts >= config.maxReconnectAttempts) {
            ElMessage.error('SSE连接重试次数已达上限')
            state.status = SSEStatus.ERROR
        }
    
        return success
    }

    // 清空事件列表
    const clearEvents = () => {
        events.value = []
        state.eventCount = 0
        state.lastEventTime = null
    }

    // 获取特定类型的事件
    const getEventsByType = (type: string): SSEEventData[] => {
        return events.value.filter(event => event.type === type)
    }

    // 获取最新的事件
    const getLatestEvent = (type?: string): SSEEventData | null => {
        if (type) {
            return events.value.find(event => event.type === type) || null
        }
        return events.value[0] || null
    }

    // 设置事件回调
    const setCallback = (eventType: keyof SSEEventCallbacks, callback: Function) => {
        callbacks[eventType] = callback as any
    }

    // 获取连接状态描述
    const getStatusText = (): string => {
        switch (state.status) {
            case SSEStatus.DISCONNECTED:
                return '未连接'
            case SSEStatus.CONNECTING:
                return '连接中...'
            case SSEStatus.CONNECTED:
                return '已连接'
            case SSEStatus.ERROR:
                return '连接错误'
            case SSEStatus.RECONNECTING:
                return `重连中 (${state.reconnectAttempts}/${config.maxReconnectAttempts})`
            default:
                return '未知状态'
        }
    }

    // 组件卸载时自动断开连接
    onUnmounted(() => {
        disconnect()
    })

    return {
    // 状态
        state: readonly(state),
        config,
        events: readonly(events),
        connecting: readonly(connecting),
    
        // 方法
        connect,
        disconnect,
        reconnect,
        clearEvents,
        getEventsByType,
        getLatestEvent,
        setCallback,
        getStatusText,
    
        // 工具方法
        addEvent
    }
}

// 导出类型
export { type SSEConfig, type SSEState }
