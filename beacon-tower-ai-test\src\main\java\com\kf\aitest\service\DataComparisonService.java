package com.kf.aitest.service;

import com.kf.aitest.dto.DataComparisonRequestDTO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 数据对比服务接口
 */
public interface DataComparisonService {
    
    /**
     * 创建SSE连接
     * 
     * @param taskId 任务ID
     * @return SSE发射器
     */
    SseEmitter createSseConnection(String taskId);
    
    /**
     * 开始数据对比任务
     * 
     * @param taskId 任务ID
     * @param request 对比请求
     */
    void startComparison(String taskId, DataComparisonRequestDTO request);
    
    /**
     * 获取默认的UAT环境URL
     * 
     * @return UAT环境URL
     */
    String getDefaultUatUrl();
    
    /**
     * 获取默认的TEST环境URL
     * 
     * @return TEST环境URL
     */
    String getDefaultTestUrl();
}
