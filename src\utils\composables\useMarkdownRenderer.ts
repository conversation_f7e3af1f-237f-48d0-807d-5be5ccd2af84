/**
 * Markdown渲染Composable
 * 提供Markdown渲染的通用逻辑和配置
 */

import { ref, reactive, computed, nextTick, readonly } from 'vue'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'

// Markdown渲染配置接口
export interface MarkdownConfig {
  html: boolean
  linkify: boolean
  typographer: boolean
  breaks: boolean
  highlight: boolean
  theme: string
}

// Markdown渲染状态接口
export interface MarkdownState {
  content: string
  renderedHtml: string
  loading: boolean
  error: string | null
  renderTime: number
}

/**
 * Markdown渲染Composable
 */
export function useMarkdownRenderer() {
    // 响应式状态
    const state = reactive<MarkdownState>({
        content: '',
        renderedHtml: '',
        loading: false,
        error: null,
        renderTime: 0
    })

    const config = reactive<MarkdownConfig>({
        html: true,
        linkify: true,
        typographer: true,
        breaks: true,
        highlight: true,
        theme: 'github'
    })

    // Markdown-it实例
    let markdownInstance: MarkdownIt | null = null

    // 计算属性
    const hasContent = computed(() => state.content.trim().length > 0)
    const hasRenderedContent = computed(() => state.renderedHtml.trim().length > 0)
    const isReady = computed(() => markdownInstance !== null)

    // 初始化Markdown-it实例
    const initMarkdown = () => {
        markdownInstance = new MarkdownIt({
            html: config.html,
            linkify: config.linkify,
            typographer: config.typographer,
            breaks: config.breaks,
            highlight: config.highlight ? (str, lang) => {
                if (lang && hljs.getLanguage(lang)) {
                    try {
                        return `<pre class="hljs"><code>${hljs.highlight(str, { language: lang, ignoreIllegals: true }).value}</code></pre>`
                    } catch (error) {
                        console.warn('Highlight.js error:', error)
                    }
                }
                return `<pre class="hljs"><code>${markdownInstance?.utils.escapeHtml(str)}</code></pre>`
            } : undefined
        })

        // 添加表格支持
        markdownInstance.enable(['table'])
    
        // 添加删除线支持
        markdownInstance.enable(['strikethrough'])
    }

    // 渲染Markdown内容
    const render = async (content: string): Promise<string> => {
        if (!content || content.trim().length === 0) {
            state.content = ''
            state.renderedHtml = ''
            return ''
        }

        state.loading = true
        state.error = null
        const startTime = performance.now()

        try {
            // 确保Markdown实例已初始化
            if (!markdownInstance) {
                initMarkdown()
            }

            if (!markdownInstance) {
                throw new Error('Markdown实例初始化失败')
            }

            // 渲染内容
            const html = markdownInstance.render(content)
      
            state.content = content
            state.renderedHtml = html
            state.renderTime = performance.now() - startTime

            return html
        } catch (error) {
            state.error = error instanceof Error ? error.message : 'Markdown渲染失败'
            console.error('Markdown渲染错误:', error)
            return ''
        } finally {
            state.loading = false
        }
    }

    // 更新配置并重新渲染
    const updateConfig = async (newConfig: Partial<MarkdownConfig>) => {
        Object.assign(config, newConfig)
    
        // 重新初始化Markdown实例
        initMarkdown()
    
        // 如果有内容，重新渲染
        if (state.content) {
            await render(state.content)
        }
    }

    // 清空内容
    const clear = () => {
        state.content = ''
        state.renderedHtml = ''
        state.error = null
        state.renderTime = 0
    }

    // 获取渲染统计信息
    const getStats = () => {
        const wordCount = state.content.split(/\s+/).filter(word => word.length > 0).length
        const charCount = state.content.length
        const lineCount = state.content.split('\n').length
    
        return {
            wordCount,
            charCount,
            lineCount,
            renderTime: state.renderTime
        }
    }

    // 预设配置
    const presets = {
    // 默认配置
        default: {
            html: true,
            linkify: true,
            typographer: true,
            breaks: true,
            highlight: true,
            theme: 'github'
        },
        // 安全配置（禁用HTML）
        safe: {
            html: false,
            linkify: true,
            typographer: true,
            breaks: true,
            highlight: true,
            theme: 'github'
        },
        // 简单配置
        simple: {
            html: false,
            linkify: false,
            typographer: false,
            breaks: false,
            highlight: false,
            theme: 'default'
        },
        // 代码友好配置
        code: {
            html: true,
            linkify: true,
            typographer: false,
            breaks: true,
            highlight: true,
            theme: 'vs2015'
        }
    }

    // 应用预设配置
    const applyPreset = async (presetName: keyof typeof presets) => {
        const preset = presets[presetName]
        if (preset) {
            await updateConfig(preset)
        }
    }

    // 导出为HTML文件
    const exportToHtml = (title: string = 'Markdown Document'): string => {
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/${config.theme}.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        pre {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            overflow-x: auto;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    ${state.renderedHtml}
</body>
</html>`
    }

    // 初始化
    initMarkdown()

    return {
    // 状态
        state: readonly(state),
        config,
    
        // 计算属性
        hasContent,
        hasRenderedContent,
        isReady,
    
        // 方法
        render,
        updateConfig,
        clear,
        getStats,
        applyPreset,
        exportToHtml,
    
        // 预设配置
        presets
    }
}

// 导出类型
export { type MarkdownConfig, type MarkdownState }
