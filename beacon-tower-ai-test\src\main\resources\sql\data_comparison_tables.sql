-- 数据对比相关表结构

-- 数据对比主表
CREATE TABLE IF NOT EXISTS t_data_comparison (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    user_id VARCHAR(64) COMMENT '用户ID',
    comparison_ids TEXT COMMENT '对比ID列表(JSON格式)',
    overall_status VARCHAR(32) COMMENT '整体状态：SUCCESS、FAILED、PARTIAL_SUCCESS',
    overall_score INT COMMENT '整体评分(0-100)',
    overall_ai_evaluation TEXT COMMENT '整体AI评估结果',
    total_duration BIGINT COMMENT '总耗时(毫秒)',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    success_stage_count INT COMMENT '成功的阶段数量',
    total_stage_count INT DEFAULT 4 COMMENT '总阶段数量',
    error_messages TEXT COMMENT '错误信息汇总(JSON格式)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status TINYINT DEFAULT 0 COMMENT '状态：0正常，1删除',
    INDEX idx_task_id (task_id),
    INDEX idx_user_id (user_id),
    INDEX idx_create_time (create_time),
    INDEX idx_overall_status (overall_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据对比主表';

-- 数据对比阶段结果表
CREATE TABLE IF NOT EXISTS t_data_comparison_stage (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    comparison_id BIGINT NOT NULL COMMENT '对比主表ID',
    data_id VARCHAR(64) NOT NULL COMMENT '对比数据ID',
    stage_name VARCHAR(32) NOT NULL COMMENT '阶段名称：recognize、extraction、structured、transformer',
    data_type VARCHAR(16) COMMENT '数据类型：json、md等',
    stage_status VARCHAR(32) COMMENT '阶段状态：SUCCESS、FAILED、TIMEOUT',
    error_message TEXT COMMENT '错误信息',
    duration BIGINT COMMENT '数据获取耗时(毫秒)',
    fetch_time DATETIME COMMENT '获取时间',
    ai_evaluation TEXT COMMENT 'AI评估结果',
    ai_score INT COMMENT 'AI评估得分(0-100)',
    uat_data LONGTEXT COMMENT 'UAT环境数据(JSON格式)',
    test_data LONGTEXT COMMENT 'TEST环境数据(JSON格式)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (comparison_id) REFERENCES t_data_comparison(id) ON DELETE CASCADE,
    INDEX idx_comparison_id (comparison_id),
    INDEX idx_data_id (data_id),
    INDEX idx_stage_name (stage_name),
    INDEX idx_stage_status (stage_status),
    INDEX idx_fetch_time (fetch_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据对比阶段结果表';

-- 插入示例数据（可选）
-- INSERT INTO t_data_comparison (task_id, user_id, comparison_ids, overall_status, overall_score, total_stage_count) 
-- VALUES ('task_001', 'user_001', '["id1", "id2"]', 'SUCCESS', 85, 4);

-- 查询示例
-- 查询用户的对比记录
-- SELECT * FROM t_data_comparison WHERE user_id = 'user_001' ORDER BY create_time DESC;

-- 查询对比的详细阶段结果
-- SELECT c.task_id, c.overall_status, s.stage_name, s.stage_status, s.ai_score 
-- FROM t_data_comparison c 
-- LEFT JOIN t_data_comparison_stage s ON c.id = s.comparison_id 
-- WHERE c.task_id = 'task_001';

-- 统计各阶段成功率
-- SELECT stage_name, 
--        COUNT(*) as total_count,
--        SUM(CASE WHEN stage_status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
--        ROUND(SUM(CASE WHEN stage_status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
-- FROM t_data_comparison_stage 
-- GROUP BY stage_name;
