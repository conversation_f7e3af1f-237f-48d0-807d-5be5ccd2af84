package com.kf.aitest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kf.aitest.dao.TDataComparisonMapper;
import com.kf.aitest.dao.TDataComparisonStageMapper;
import com.kf.aitest.dto.DataComparisonResultDTO;
import com.kf.aitest.dto.StageDataDTO;
import com.kf.aitest.entity.TDataComparison;
import com.kf.aitest.entity.TDataComparisonStage;
import com.kf.aitest.service.DataComparisonStorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据对比存储服务实现类
 */
@Slf4j
@Service
public class DataComparisonStorageServiceImpl implements DataComparisonStorageService {

    @Autowired
    private TDataComparisonMapper comparisonMapper;

    @Autowired
    private TDataComparisonStageMapper stageMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TDataComparison saveComparisonResult(DataComparisonResultDTO resultDTO, String userId) {
        log.info("保存对比结果: taskId={}, userId={}", resultDTO.getTaskId(), userId);

        try {
            TDataComparison comparison = new TDataComparison();
            comparison.setTaskId(resultDTO.getTaskId());
            comparison.setUserId(userId);
            comparison.setComparisonIds(resultDTO.getId());
            comparison.setOverallStatus(resultDTO.getOverallStatus());
            comparison.setOverallScore(resultDTO.getOverallScore());
            comparison.setOverallAiEvaluation(resultDTO.getOverallAiEvaluation());
            comparison.setTotalDuration(resultDTO.getTotalDuration());
            comparison.setStartTime(resultDTO.getStartTime());
            comparison.setEndTime(resultDTO.getEndTime());
            comparison.setSuccessStageCount(resultDTO.getSuccessStageCount());
            comparison.setTotalStageCount(resultDTO.getTotalStageCount());
            comparison.setStatus(0);

            // 转换错误信息为JSON
            if (resultDTO.getErrors() != null && !resultDTO.getErrors().isEmpty()) {
                comparison.setErrorMessages(objectMapper.writeValueAsString(resultDTO.getErrors()));
            }

            comparisonMapper.insert(comparison);
            log.info("对比结果保存成功: id={}", comparison.getId());

            return comparison;

        } catch (JsonProcessingException e) {
            log.error("保存对比结果失败，JSON序列化错误: {}", e.getMessage(), e);
            throw new RuntimeException("保存对比结果失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("保存对比结果失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存对比结果失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TDataComparisonStage saveStageResult(Long comparisonId, String dataId, StageDataDTO stageData) {
        log.info("保存阶段结果: comparisonId={}, dataId={}, stage={}", 
                comparisonId, dataId, stageData.getStageName());

        try {
            TDataComparisonStage stage = new TDataComparisonStage();
            stage.setComparisonId(comparisonId);
            stage.setDataId(dataId);
            stage.setStageName(stageData.getStageName());
            stage.setDataType(stageData.getDataType());
            stage.setStageStatus(stageData.getStatus());
            stage.setErrorMessage(stageData.getErrorMessage());
            stage.setDuration(stageData.getDuration());
            stage.setFetchTime(stageData.getFetchTime());
            stage.setAiEvaluation(stageData.getAiEvaluation());
            stage.setAiScore(stageData.getAiScore());

            // 转换数据为JSON
            if (stageData.getUatData() != null) {
                stage.setUatData(objectMapper.writeValueAsString(stageData.getUatData()));
            }
            if (stageData.getTestData() != null) {
                stage.setTestData(objectMapper.writeValueAsString(stageData.getTestData()));
            }

            stageMapper.insert(stage);
            log.info("阶段结果保存成功: id={}", stage.getId());

            return stage;

        } catch (JsonProcessingException e) {
            log.error("保存阶段结果失败，JSON序列化错误: {}", e.getMessage(), e);
            throw new RuntimeException("保存阶段结果失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("保存阶段结果失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存阶段结果失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TDataComparisonStage> batchSaveStageResults(Long comparisonId, List<StageDataDTO> stageDataList) {
        log.info("批量保存阶段结果: comparisonId={}, count={}", comparisonId, stageDataList.size());

        List<TDataComparisonStage> savedStages = new ArrayList<>();
        for (StageDataDTO stageData : stageDataList) {
            // 从stageData中提取dataId，这里假设可以从某个字段获取
            String dataId = extractDataIdFromStageData(stageData);
            TDataComparisonStage stage = saveStageResult(comparisonId, dataId, stageData);
            savedStages.add(stage);
        }

        log.info("批量保存阶段结果完成: count={}", savedStages.size());
        return savedStages;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TDataComparison updateComparisonResult(Long comparisonId, DataComparisonResultDTO resultDTO) {
        log.info("更新对比结果: comparisonId={}", comparisonId);

        try {
            TDataComparison comparison = comparisonMapper.selectById(comparisonId);
            if (comparison == null) {
                throw new RuntimeException("对比记录不存在: " + comparisonId);
            }

            comparison.setOverallStatus(resultDTO.getOverallStatus());
            comparison.setOverallScore(resultDTO.getOverallScore());
            comparison.setOverallAiEvaluation(resultDTO.getOverallAiEvaluation());
            comparison.setTotalDuration(resultDTO.getTotalDuration());
            comparison.setEndTime(resultDTO.getEndTime());
            comparison.setSuccessStageCount(resultDTO.getSuccessStageCount());

            // 更新错误信息
            if (resultDTO.getErrors() != null && !resultDTO.getErrors().isEmpty()) {
                comparison.setErrorMessages(objectMapper.writeValueAsString(resultDTO.getErrors()));
            }

            comparisonMapper.updateById(comparison);
            log.info("对比结果更新成功: id={}", comparison.getId());

            return comparison;

        } catch (JsonProcessingException e) {
            log.error("更新对比结果失败，JSON序列化错误: {}", e.getMessage(), e);
            throw new RuntimeException("更新对比结果失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("更新对比结果失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新对比结果失败: " + e.getMessage(), e);
        }
    }

    @Override
    public DataComparisonResultDTO getComparisonResultByTaskId(String taskId) {
        log.info("根据任务ID查询对比结果: taskId={}", taskId);

        QueryWrapper<TDataComparison> wrapper = new QueryWrapper<>();
        wrapper.eq("task_id", taskId)
               .eq("status", 0)
               .orderByDesc("create_time")
               .last("LIMIT 1");

        TDataComparison comparison = comparisonMapper.selectOne(wrapper);
        if (comparison == null) {
            log.warn("未找到对比结果: taskId={}", taskId);
            return null;
        }

        return convertToDTO(comparison);
    }

    @Override
    public DataComparisonResultDTO getComparisonResultById(Long comparisonId) {
        log.info("根据ID查询对比结果: comparisonId={}", comparisonId);

        TDataComparison comparison = comparisonMapper.selectById(comparisonId);
        if (comparison == null) {
            log.warn("未找到对比结果: comparisonId={}", comparisonId);
            return null;
        }

        return convertToDTO(comparison);
    }

    @Override
    public IPage<TDataComparison> getComparisonPage(Page<TDataComparison> page, String userId, 
            String taskId, String overallStatus, LocalDateTime startTime, LocalDateTime endTime) {
        log.info("分页查询对比记录: userId={}, taskId={}, status={}", userId, taskId, overallStatus);

        return comparisonMapper.selectComparisonPage(page, userId, taskId, overallStatus, startTime, endTime);
    }

    @Override
    public List<TDataComparison> getRecentComparisons(String userId, Integer limit) {
        log.info("查询用户最近对比记录: userId={}, limit={}", userId, limit);

        return comparisonMapper.selectRecentByUserId(userId, limit);
    }

    @Override
    public Long countUserComparisons(String userId) {
        log.info("统计用户对比记录数量: userId={}", userId);

        return comparisonMapper.countByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteComparison(Long comparisonId) {
        log.info("删除对比记录: comparisonId={}", comparisonId);

        TDataComparison comparison = comparisonMapper.selectById(comparisonId);
        if (comparison == null) {
            log.warn("对比记录不存在: comparisonId={}", comparisonId);
            return false;
        }

        comparison.setStatus(1);
        int result = comparisonMapper.updateById(comparison);
        
        log.info("对比记录删除结果: comparisonId={}, result={}", comparisonId, result > 0);
        return result > 0;
    }

    @Override
    public TDataComparisonStage getStageResult(Long comparisonId, String stageName) {
        log.info("查询阶段结果: comparisonId={}, stageName={}", comparisonId, stageName);

        return stageMapper.selectByComparisonIdAndStage(comparisonId, stageName);
    }

    @Override
    public List<TDataComparisonStage> getAllStageResults(Long comparisonId) {
        log.info("查询所有阶段结果: comparisonId={}", comparisonId);

        return stageMapper.selectByComparisonId(comparisonId);
    }

    @Override
    public String exportComparisonResult(Long comparisonId) {
        log.info("导出对比结果: comparisonId={}", comparisonId);

        try {
            DataComparisonResultDTO result = getComparisonResultById(comparisonId);
            if (result == null) {
                throw new RuntimeException("对比结果不存在: " + comparisonId);
            }

            return objectMapper.writeValueAsString(result);

        } catch (JsonProcessingException e) {
            log.error("导出对比结果失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出对比结果失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将实体转换为DTO
     */
    private DataComparisonResultDTO convertToDTO(TDataComparison comparison) {
        try {
            DataComparisonResultDTO dto = new DataComparisonResultDTO();
            dto.setId(comparison.getComparisonIds());
            dto.setTaskId(comparison.getTaskId());
            dto.setOverallStatus(comparison.getOverallStatus());
            dto.setOverallScore(comparison.getOverallScore());
            dto.setOverallAiEvaluation(comparison.getOverallAiEvaluation());
            dto.setTotalDuration(comparison.getTotalDuration());
            dto.setStartTime(comparison.getStartTime());
            dto.setEndTime(comparison.getEndTime());
            dto.setSuccessStageCount(comparison.getSuccessStageCount());
            dto.setTotalStageCount(comparison.getTotalStageCount());

            // 转换错误信息
            if (comparison.getErrorMessages() != null) {
                List<String> errors = objectMapper.readValue(comparison.getErrorMessages(), 
                        objectMapper.getTypeFactory().constructCollectionType(List.class, String.class));
                dto.setErrors(errors);
            }

            // 查询阶段结果
            List<TDataComparisonStage> stages = stageMapper.selectByComparisonId(comparison.getId());
            List<StageDataDTO> stageResults = stages.stream()
                    .map(this::convertStageToDTO)
                    .collect(Collectors.toList());
            dto.setStageResults(stageResults);

            return dto;

        } catch (JsonProcessingException e) {
            log.error("转换DTO失败: {}", e.getMessage(), e);
            throw new RuntimeException("转换DTO失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将阶段实体转换为DTO
     */
    private StageDataDTO convertStageToDTO(TDataComparisonStage stage) {
        try {
            StageDataDTO dto = new StageDataDTO();
            dto.setStageName(stage.getStageName());
            dto.setDataType(stage.getDataType());
            dto.setStatus(stage.getStageStatus());
            dto.setErrorMessage(stage.getErrorMessage());
            dto.setDuration(stage.getDuration());
            dto.setFetchTime(stage.getFetchTime());
            dto.setAiEvaluation(stage.getAiEvaluation());
            dto.setAiScore(stage.getAiScore());

            // 转换数据
            if (stage.getUatData() != null) {
                dto.setUatData(objectMapper.readValue(stage.getUatData(), Object.class));
            }
            if (stage.getTestData() != null) {
                dto.setTestData(objectMapper.readValue(stage.getTestData(), Object.class));
            }

            return dto;

        } catch (JsonProcessingException e) {
            log.error("转换阶段DTO失败: {}", e.getMessage(), e);
            throw new RuntimeException("转换阶段DTO失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从阶段数据中提取数据ID
     */
    private String extractDataIdFromStageData(StageDataDTO stageData) {
        // 这里需要根据实际业务逻辑来提取dataId
        // 可能从stageData的某个字段或者通过其他方式获取
        return "data_" + System.currentTimeMillis();
    }
}
